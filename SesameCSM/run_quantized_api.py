#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to run the CSM Voice API with 4-bit quantization enabled.
This demonstrates the memory savings and performance characteristics of the quantized model.
"""

import os
import sys
import subprocess
import time

def run_quantized_api():
    """Run the voice API with 4-bit quantization enabled."""
    print("🚀 Starting CSM Voice API with 4-bit quantization...")
    
    # Set environment variable to enable quantization
    env = os.environ.copy()
    env["USE_4BIT_QUANTIZATION"] = "true"
    
    # Change to the csm directory
    csm_dir = os.path.join(os.path.dirname(__file__), "csm")
    
    try:
        # Run the API
        cmd = [sys.executable, "voice_api.py"]
        print(f"📡 Running command: {' '.join(cmd)}")
        print(f"📁 Working directory: {csm_dir}")
        print(f"🔧 Quantization enabled: {env.get('USE_4BIT_QUANTIZATION', 'false')}")
        print("="*60)
        
        process = subprocess.run(
            cmd,
            cwd=csm_dir,
            env=env,
            check=True
        )
        
    except KeyboardInterrupt:
        print("\n🛑 API stopped by user")
    except subprocess.CalledProcessError as e:
        print(f"❌ API failed with exit code {e.returncode}")
        sys.exit(e.returncode)
    except Exception as e:
        print(f"❌ Error running API: {e}")
        sys.exit(1)

if __name__ == "__main__":
    print("CSM Voice API - 4-bit Quantized Version")
    print("="*50)
    print("This script runs the voice API with 4-bit quantization enabled.")
    print("Expected benefits:")
    print("  • Reduced GPU memory usage (~50% reduction)")
    print("  • Faster model loading")
    print("  • Slightly slower inference (but still real-time)")
    print("  • Minimal quality degradation")
    print("="*50)
    
    run_quantized_api()
