#!/usr/bin/env python3
"""
Test script to verify the concurrency fix for the CSM Voice API.
This script sends multiple concurrent requests to test that the API handles them properly.
"""

import asyncio
import aiohttp
import time
import logging
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

API_BASE_URL = "http://localhost:8000"

async def send_request(session: aiohttp.ClientSession, request_id: int, test_audio_path: str):
    """Send a single voice generation request."""
    try:
        logger.info(f"🚀 Request {request_id}: Starting")
        start_time = time.time()
        
        # Prepare the request data
        data = aiohttp.FormData()
        data.add_field('text', f'Hello from request number {request_id}. This is a test of concurrent voice generation.')
        data.add_field('max_audio_length_ms', '10000')
        
        # Add the audio file
        with open(test_audio_path, 'rb') as f:
            data.add_field('voice_file', f, filename='test_voice.wav', content_type='audio/wav')
            
            async with session.post(f"{API_BASE_URL}/generate-voice", data=data) as response:
                if response.status == 200:
                    # Save the response audio
                    output_path = f"output_request_{request_id}.wav"
                    with open(output_path, 'wb') as output_file:
                        async for chunk in response.content.iter_chunked(8192):
                            output_file.write(chunk)
                    
                    duration = time.time() - start_time
                    generation_time = response.headers.get('X-Generation-Time', 'unknown')
                    request_id_header = response.headers.get('X-Request-ID', 'unknown')
                    
                    logger.info(f"✅ Request {request_id}: SUCCESS in {duration:.2f}s (gen: {generation_time}s, id: {request_id_header})")
                    return True, duration, generation_time
                else:
                    error_text = await response.text()
                    logger.error(f"❌ Request {request_id}: FAILED with status {response.status}: {error_text}")
                    return False, time.time() - start_time, None
                    
    except Exception as e:
        duration = time.time() - start_time
        logger.error(f"❌ Request {request_id}: EXCEPTION after {duration:.2f}s: {str(e)}")
        return False, duration, None

async def check_health(session: aiohttp.ClientSession):
    """Check API health and queue status."""
    try:
        async with session.get(f"{API_BASE_URL}/health") as response:
            if response.status == 200:
                health_data = await response.json()
                logger.info(f"🏥 Health check: {health_data}")
                return health_data
    except Exception as e:
        logger.error(f"❌ Health check failed: {e}")
        return None

async def check_queue_status(session: aiohttp.ClientSession):
    """Check queue status."""
    try:
        async with session.get(f"{API_BASE_URL}/queue-status") as response:
            if response.status == 200:
                queue_data = await response.json()
                logger.info(f"📊 Queue status: {queue_data}")
                return queue_data
    except Exception as e:
        logger.error(f"❌ Queue status check failed: {e}")
        return None

async def run_concurrency_test(num_requests: int = 5, test_audio_path: str = None):
    """Run the concurrency test."""
    if not test_audio_path or not Path(test_audio_path).exists():
        logger.error(f"❌ Test audio file not found: {test_audio_path}")
        logger.info("Please provide a valid WAV file path for testing")
        return
    
    logger.info(f"🧪 Starting concurrency test with {num_requests} concurrent requests")
    
    async with aiohttp.ClientSession() as session:
        # Check initial health
        await check_health(session)
        await check_queue_status(session)
        
        # Send concurrent requests
        start_time = time.time()
        tasks = [
            send_request(session, i+1, test_audio_path) 
            for i in range(num_requests)
        ]
        
        # Wait for all requests to complete
        results = await asyncio.gather(*tasks, return_exceptions=True)
        total_time = time.time() - start_time
        
        # Analyze results
        successful = sum(1 for result in results if isinstance(result, tuple) and result[0])
        failed = len(results) - successful
        
        logger.info(f"📈 Test completed in {total_time:.2f}s")
        logger.info(f"✅ Successful requests: {successful}/{num_requests}")
        logger.info(f"❌ Failed requests: {failed}/{num_requests}")
        
        if successful > 0:
            durations = [result[1] for result in results if isinstance(result, tuple)]
            avg_duration = sum(durations) / len(durations)
            logger.info(f"⏱️ Average request duration: {avg_duration:.2f}s")
        
        # Final health check
        await check_health(session)
        await check_queue_status(session)

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) < 2:
        print("Usage: python test_concurrency.py <path_to_test_audio.wav> [num_requests]")
        print("Example: python test_concurrency.py test_voice.wav 5")
        sys.exit(1)
    
    test_audio_path = sys.argv[1]
    num_requests = int(sys.argv[2]) if len(sys.argv) > 2 else 5
    
    asyncio.run(run_concurrency_test(num_requests, test_audio_path))
