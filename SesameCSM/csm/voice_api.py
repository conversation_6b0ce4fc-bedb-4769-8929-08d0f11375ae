import os
import time
import tempfile
import uuid
import re
import logging
import asyncio
from typing import Optional

import torch
import torchaudio
from fastapi import FastAPI, File, UploadFile, Form, HTTPException
from fastapi.responses import FileResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from faster_whisper import WhisperModel

from generator import load_csm_1b, Segment

# 🔒 Set visible CUDA device to GPU 1 (makes it appear as cuda:0)
os.environ["CUDA_VISIBLE_DEVICES"] = "1"
# 🔧 Disable torch.compile (TorchDynamo) entirely
os.environ["TORCH_DISABLE_DYNAMO"] = "1"
os.environ["NO_TORCH_COMPILE"] = "1"

# 🛡️ Suppress TorchDynamo internal fallback crashes
import torch._dynamo
torch._dynamo.config.suppress_errors = True

# 🧹 Clear GPU memory before loading model
torch.cuda.empty_cache()
torch.cuda.reset_peak_memory_stats()

# ✅ Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s | %(levelname)s | %(name)s | %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger("CSMVoiceAPI")

# 🎤 FastAPI app
app = FastAPI(title="CSM Voice Generation API", version="1.0.0")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Optional: Request logging middleware with concurrency info
@app.middleware("http")
async def log_requests(request, call_next):
    global active_requests
    logger.info(f"📡 Incoming request: {request.method} {request.url} (Active: {active_requests})")
    start_time = time.time()
    response = await call_next(request)
    duration = time.time() - start_time
    logger.info(f"↩️ Completed response: {response.status_code} for {request.url} in {duration:.2f}s")
    return response

# Global variables for models
generator = None
whisper_model = None
device = None

# 🔒 Concurrency control: Locks to prevent simultaneous model access
# These locks ensure only one request can use each model at a time
csm_model_lock = asyncio.Lock()
whisper_model_lock = asyncio.Lock()

# 📊 Request tracking for monitoring
active_requests = 0
total_requests = 0
request_queue_size = 0

def load_prompt_audio(audio_path: str, target_sample_rate: int) -> torch.Tensor:
    """Load and resample audio to target sample rate."""
    audio_tensor, sample_rate = torchaudio.load(audio_path)
    audio_tensor = audio_tensor.squeeze(0)
    audio_tensor = torchaudio.functional.resample(
        audio_tensor, orig_freq=sample_rate, new_freq=target_sample_rate
    )
    return audio_tensor

async def transcribe_and_clean_audio(audio_path: str) -> str:
    """Transcribe audio file and clean the text."""
    global whisper_model
    logger.info("🎤 Transcribing audio file...")

    # 🔒 Acquire whisper model lock to prevent concurrent access
    async with whisper_model_lock:
        logger.info("🔓 Acquired Whisper model lock")
        segments, info = whisper_model.transcribe(audio_path, beam_size=5)
        logger.info(f"🗣️ Detected language: {info.language}")

        full_text = " ".join(segment.text for segment in segments)
        cleaned_text = re.sub(r'[?.!,;:\']', '', full_text.strip()).lower()
        cleaned_text = re.sub(r'\bits\b', 'its', cleaned_text)

        logger.info(f"📝 Transcribed text: {cleaned_text}")
        return cleaned_text

def prepare_prompt(text: str, speaker: int, audio_path: str, sample_rate: int) -> Segment:
    """Prepare a voice prompt segment."""
    audio_tensor = load_prompt_audio(audio_path, sample_rate)
    return Segment(text=text, speaker=speaker, audio=audio_tensor)

async def generate_speech_safely(
    text: str,
    speaker: int,
    context: list,
    max_audio_length_ms: int
) -> torch.Tensor:
    """Thread-safe wrapper for CSM model generation."""
    global generator

    # 🔒 Acquire CSM model lock to prevent concurrent access
    async with csm_model_lock:
        logger.info("🔓 Acquired CSM model lock")

        # Reset model caches to ensure clean state
        generator._model.reset_caches()

        # Generate audio with the model
        audio_tensor = generator.generate(
            text=text,
            speaker=speaker,
            context=context,
            max_audio_length_ms=max_audio_length_ms,
        )

        logger.info("🔒 Released CSM model lock")
        return audio_tensor

@app.on_event("startup")
async def startup_event():
    """Initialize the models on startup."""
    global generator, whisper_model, device

    device = "cuda" if torch.cuda.is_available() else "cpu"
    logger.info(f"🚀 Starting API with device: {device}")

    try:
        logger.info("Loading Whisper model...")
        whisper_load_start = time.time()
        whisper_model = WhisperModel("base", device=device, compute_type="float16")
        whisper_load_time = time.time() - whisper_load_start
        logger.info(f"✅ Whisper model loaded in {whisper_load_time:.2f} seconds")
    except Exception:
        logger.exception("❌ Failed to load Whisper model")

    try:
        logger.info("Loading CSM model...")
        model_load_start = time.time()
        generator = load_csm_1b(device)
        model_load_time = time.time() - model_load_start
        logger.info(f"✅ CSM model loaded in {model_load_time:.2f} seconds")
    except Exception:
        logger.exception("❌ Failed to load CSM model")

@app.get("/")
async def root():
    """Health check endpoint."""
    return {
        "message": "CSM Voice Generation API is running",
        "device": device,
        "csm_model_loaded": generator is not None,
        "whisper_model_loaded": whisper_model is not None
    }

@app.post("/generate-voice")
async def generate_voice(
    text: str = Form(..., description="Text to generate speech for"),
    voice_file: UploadFile = File(..., description="Voice sample file (WAV format)"),
    max_audio_length_ms: Optional[int] = Form(30000, description="Maximum audio length in milliseconds")
):
    """Generate speech using the provided voice sample."""
    global active_requests, total_requests, request_queue_size

    # 📊 Track request metrics
    total_requests += 1
    request_id = f"req_{total_requests:04d}"
    logger.info(f"🚀 [{request_id}] Starting voice generation request")

    if generator is None or whisper_model is None:
        raise HTTPException(status_code=503, detail="Models not loaded yet")

    if not voice_file.filename.lower().endswith(('.wav', '.mp3', '.flac', '.m4a')):
        raise HTTPException(status_code=400, detail="Voice file must be an audio file (WAV, MP3, FLAC, M4A)")

    # 📊 Update active request count
    active_requests += 1
    logger.info(f"📈 [{request_id}] Active requests: {active_requests}")

    try:
        logger.info(f"📥 [{request_id}] Received voice sample: {voice_file.filename}")
        with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as temp_voice_file:
            content = await voice_file.read()
            temp_voice_file.write(content)
            temp_voice_path = temp_voice_file.name

        # 🎤 Transcribe audio using thread-safe function
        voice_text = await transcribe_and_clean_audio(temp_voice_path)

        logger.info(f"🎵 [{request_id}] Preparing voice prompt from uploaded file...")
        prompt_segment = prepare_prompt(
            text=voice_text,
            speaker=0,
            audio_path=temp_voice_path,
            sample_rate=generator.sample_rate
        )

        logger.info(f"🎵 [{request_id}] Generating speech for: {text[:50]}...")
        generation_start = time.time()

        # 🎵 Generate audio using thread-safe function
        audio_tensor = await generate_speech_safely(
            text=text,
            speaker=0,
            context=[prompt_segment],
            max_audio_length_ms=max_audio_length_ms,
        )

        generation_time = time.time() - generation_start
        logger.info(f"✅ [{request_id}] Speech generated in {generation_time:.2f} seconds")

        output_filename = f"generated_speech_{uuid.uuid4().hex[:8]}.wav"
        output_path = f"/tmp/{output_filename}"

        torchaudio.save(
            output_path,
            audio_tensor.unsqueeze(0).cpu(),
            generator.sample_rate
        )

        logger.info(f"💾 [{request_id}] Saved generated file to {output_path}")
        os.unlink(temp_voice_path)

        # 📊 Update request metrics before returning
        active_requests -= 1
        logger.info(f"📉 [{request_id}] Request completed. Active requests: {active_requests}")

        return FileResponse(
            output_path,
            media_type="audio/wav",
            filename=output_filename,
            headers={
                "X-Generation-Time": str(generation_time),
                "X-Request-ID": request_id
            }
        )

    except Exception as e:
        # 📊 Update request metrics on error
        active_requests -= 1
        logger.exception(f"❌ [{request_id}] Error during voice generation")
        if 'temp_voice_path' in locals() and os.path.exists(temp_voice_path):
            os.unlink(temp_voice_path)
        raise HTTPException(status_code=500, detail=f"Error generating speech: {str(e)}")

@app.get("/health")
async def health_check():
    """Detailed health check."""
    global active_requests, total_requests
    logger.info("🔍 Health check requested")

    # Check if models are currently locked
    csm_locked = csm_model_lock.locked()
    whisper_locked = whisper_model_lock.locked()

    return {
        "status": "healthy",
        "device": device,
        "csm_model_loaded": generator is not None,
        "whisper_model_loaded": whisper_model is not None,
        "cuda_available": torch.cuda.is_available(),
        "gpu_memory": torch.cuda.get_device_properties(0).total_memory if torch.cuda.is_available() else None,
        "concurrency_info": {
            "active_requests": active_requests,
            "total_requests": total_requests,
            "csm_model_locked": csm_locked,
            "whisper_model_locked": whisper_locked
        }
    }

@app.get("/queue-status")
async def queue_status():
    """Get current queue and concurrency status."""
    global active_requests, total_requests

    return {
        "active_requests": active_requests,
        "total_requests": total_requests,
        "csm_model_locked": csm_model_lock.locked(),
        "whisper_model_locked": whisper_model_lock.locked(),
        "queue_info": {
            "csm_waiters": len(csm_model_lock._waiters) if hasattr(csm_model_lock, '_waiters') else 0,
            "whisper_waiters": len(whisper_model_lock._waiters) if hasattr(whisper_model_lock, '_waiters') else 0
        }
    }

if __name__ == "__main__":
    uvicorn.run(
        "voice_api:app",
        host="0.0.0.0",
        port=8000,
        reload=False
    )
