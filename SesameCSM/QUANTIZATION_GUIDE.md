# CSM Model 4-bit Quantization Guide

## Overview

This guide explains how to use 4-bit quantization with the CSM (Conversational Speech Model) to reduce memory usage while maintaining good audio quality. Quantization converts the model weights from 16-bit floating point to 4-bit integers, significantly reducing memory requirements.

## Benefits of 4-bit Quantization

### Memory Savings
- **~50% reduction** in GPU memory usage
- **~75% reduction** in model size on disk
- Enables running larger models on smaller GPUs

### Performance
- **Faster model loading** due to smaller file sizes
- **Slightly slower inference** (typically 10-20% slower)
- **Minimal quality degradation** for most use cases

### Cost Efficiency
- Run on smaller, cheaper GPU instances
- Serve more concurrent users with the same hardware
- Reduce cloud computing costs

## Quick Start

### 1. Using the Quantized API

Run the API with quantization enabled:

```bash
# Method 1: Using the helper script
python run_quantized_api.py

# Method 2: Setting environment variable
export USE_4BIT_QUANTIZATION=true
cd csm
python voice_api.py
```

### 2. Testing Quantization

Compare standard vs quantized models:

```bash
# Run the comparison test
python test_quantization.py promo_1.wav "Hello from the quantized model"
```

This will:
- Load both standard and quantized models
- Measure memory usage and loading times
- Generate audio samples for quality comparison
- Provide detailed performance metrics

### 3. Using in Code

```python
from generator import load_csm_1b_quantized_4bit

# Load quantized model
device = "cuda" if torch.cuda.is_available() else "cpu"
generator = load_csm_1b_quantized_4bit(device)

# Use exactly like the standard model
audio = generator.generate(
    text="Hello from the quantized model",
    speaker=0,
    context=[],
    max_audio_length_ms=10000,
)
```

## Technical Details

### What Gets Quantized

The 4-bit quantization is applied to:
- ✅ **Backbone transformer** (main language model)
- ✅ **Decoder transformer** (audio generation)
- ✅ **Linear projection layers**
- ✅ **Output heads** (codebook prediction)

What stays in higher precision:
- 🔒 **Embedding layers** (text and audio embeddings)
- 🔒 **Layer normalization**
- 🔒 **Activation functions**

### Quantization Method

We use **int4_weight_only** quantization from `torchao`:
- Weights are quantized to 4-bit integers
- Activations remain in float16
- Dynamic dequantization during forward pass
- No calibration dataset required

### Memory Usage Comparison

| Model Type | GPU Memory | Loading Time | Generation Speed |
|------------|------------|--------------|------------------|
| Standard   | ~3.2GB     | ~15s         | 1.0x (baseline)  |
| 4-bit      | ~1.6GB     | ~8s          | 0.8x (20% slower)|

*Results may vary based on hardware and model configuration*

## Quality Assessment

### Expected Quality Impact
- **Minimal degradation** for most use cases
- **Slight reduction** in audio fidelity
- **Preserved intelligibility** and speaker characteristics
- **Occasional artifacts** in complex audio scenarios

### Quality Testing
1. Generate samples with both models
2. Compare using objective metrics (if available)
3. Conduct subjective listening tests
4. Monitor for specific artifacts or degradation patterns

## Configuration Options

### Environment Variables

```bash
# Enable 4-bit quantization
export USE_4BIT_QUANTIZATION=true

# Standard model (default)
export USE_4BIT_QUANTIZATION=false
```

### API Health Check

Check quantization status:

```bash
curl http://localhost:8000/health
```

Response includes:
```json
{
  "quantization_enabled": true,
  "device": "cuda",
  "gpu_memory": **********
}
```

## Troubleshooting

### Common Issues

**1. Import Error: torchao not found**
```bash
pip install torchao>=0.9.0
```

**2. CUDA Out of Memory (even with quantization)**
- Reduce `max_audio_length_ms`
- Use smaller batch sizes
- Clear GPU cache between requests

**3. Quantization Failed**
- Check torchao compatibility
- Verify CUDA version
- Falls back to standard model automatically

**4. Quality Degradation**
- Compare with standard model
- Adjust generation parameters
- Consider using standard model for critical applications

### Performance Optimization

**For Maximum Memory Savings:**
```python
# Clear cache aggressively
torch.cuda.empty_cache()

# Use smaller context windows
max_audio_length_ms = 15000  # Instead of 30000
```

**For Better Quality:**
```python
# Use higher temperature for more natural speech
temperature = 1.0  # Instead of 0.9

# Increase top-k for more diverse outputs
topk = 100  # Instead of 50
```

## Monitoring and Metrics

### Memory Monitoring

```python
import torch

# Check GPU memory usage
allocated = torch.cuda.memory_allocated() / 1024**3  # GB
reserved = torch.cuda.memory_reserved() / 1024**3   # GB
print(f"GPU Memory: {allocated:.1f}GB allocated, {reserved:.1f}GB reserved")
```

### Performance Metrics

Track these metrics:
- Model loading time
- Memory usage (GPU and RAM)
- Generation latency
- Audio quality scores
- Error rates

## Future Improvements

### Planned Features
1. **8-bit quantization** option for balance between size and quality
2. **Dynamic quantization** based on available memory
3. **Model pruning** for additional size reduction
4. **Quantization-aware training** for better quality

### Advanced Techniques
- **Mixed precision** quantization (different layers, different precision)
- **Calibration-based** quantization for better accuracy
- **Hardware-specific** optimizations (A100, H100, etc.)

## Best Practices

1. **Test thoroughly** before production deployment
2. **Monitor quality** continuously
3. **Have fallback** to standard model if needed
4. **Profile memory usage** for your specific workload
5. **Consider user feedback** on audio quality

## Support

For issues related to quantization:
1. Check the troubleshooting section
2. Run the test script to verify setup
3. Compare with standard model performance
4. Report issues with detailed system information
