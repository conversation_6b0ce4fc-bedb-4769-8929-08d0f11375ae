#!/usr/bin/env python3
"""
Test script to compare 4-bit quantized vs standard CSM model.
This script measures memory usage, loading time, and generation quality.
"""

import os
import sys
import time
import torch
import torchaudio
import psutil
import gc
from pathlib import Path

# Add csm directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'csm'))

# Set environment variables before importing
os.environ["CUDA_VISIBLE_DEVICES"] = "1"
os.environ["TORCH_DISABLE_DYNAMO"] = "1"
os.environ["NO_TORCH_COMPILE"] = "1"

import torch._dynamo
torch._dynamo.config.suppress_errors = True

from generator import load_csm_1b, load_csm_1b_quantized_4bit, Segment

def get_memory_usage():
    """Get current memory usage in MB."""
    process = psutil.Process(os.getpid())
    memory_info = process.memory_info()
    return {
        "ram_mb": memory_info.rss / 1024 / 1024,
        "gpu_mb": torch.cuda.memory_allocated() / 1024 / 1024 if torch.cuda.is_available() else 0,
        "gpu_reserved_mb": torch.cuda.memory_reserved() / 1024 / 1024 if torch.cuda.is_available() else 0
    }

def clear_memory():
    """Clear GPU and system memory."""
    gc.collect()
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        torch.cuda.reset_peak_memory_stats()

def load_test_audio(audio_path: str, target_sample_rate: int) -> torch.Tensor:
    """Load and resample test audio."""
    audio_tensor, sample_rate = torchaudio.load(audio_path)
    audio_tensor = audio_tensor.squeeze(0)
    audio_tensor = torchaudio.functional.resample(
        audio_tensor, orig_freq=sample_rate, new_freq=target_sample_rate
    )
    return audio_tensor

def test_model_loading(model_type: str, device: str = "cuda"):
    """Test model loading time and memory usage."""
    print(f"\n{'='*50}")
    print(f"Testing {model_type} model loading")
    print(f"{'='*50}")
    
    # Clear memory before test
    clear_memory()
    initial_memory = get_memory_usage()
    print(f"Initial memory: RAM={initial_memory['ram_mb']:.1f}MB, GPU={initial_memory['gpu_mb']:.1f}MB")
    
    # Load model
    start_time = time.time()
    try:
        if model_type == "standard":
            generator = load_csm_1b(device)
        elif model_type == "4bit_quantized":
            generator = load_csm_1b_quantized_4bit(device)
        else:
            raise ValueError(f"Unknown model type: {model_type}")
            
        load_time = time.time() - start_time
        post_load_memory = get_memory_usage()
        
        print(f"✅ {model_type} model loaded successfully in {load_time:.2f} seconds")
        print(f"Post-load memory: RAM={post_load_memory['ram_mb']:.1f}MB, GPU={post_load_memory['gpu_mb']:.1f}MB")
        print(f"Memory increase: RAM={post_load_memory['ram_mb'] - initial_memory['ram_mb']:.1f}MB, "
              f"GPU={post_load_memory['gpu_mb'] - initial_memory['gpu_mb']:.1f}MB")
        
        return generator, load_time, post_load_memory
        
    except Exception as e:
        print(f"❌ Failed to load {model_type} model: {e}")
        return None, None, None

def test_generation(generator, model_type: str, test_text: str, test_audio_path: str):
    """Test audio generation performance."""
    if generator is None:
        print(f"❌ Cannot test generation for {model_type} - model not loaded")
        return None, None
    
    print(f"\n🎵 Testing {model_type} generation...")
    
    try:
        # Prepare test prompt
        audio_tensor = load_test_audio(test_audio_path, generator.sample_rate)
        prompt_segment = Segment(
            text="This is a test prompt for voice generation.",
            speaker=0,
            audio=audio_tensor
        )
        
        # Generate audio
        start_time = time.time()
        pre_gen_memory = get_memory_usage()
        
        generated_audio = generator.generate(
            text=test_text,
            speaker=0,
            context=[prompt_segment],
            max_audio_length_ms=10000,  # 10 seconds
        )
        
        generation_time = time.time() - start_time
        post_gen_memory = get_memory_usage()
        
        # Save output
        output_path = f"test_output_{model_type}.wav"
        torchaudio.save(
            output_path,
            generated_audio.unsqueeze(0).cpu(),
            generator.sample_rate
        )
        
        print(f"✅ {model_type} generation completed in {generation_time:.2f} seconds")
        print(f"Generated audio length: {len(generated_audio) / generator.sample_rate:.2f} seconds")
        print(f"Memory during generation: GPU={post_gen_memory['gpu_mb']:.1f}MB")
        print(f"Output saved to: {output_path}")
        
        return generation_time, output_path
        
    except Exception as e:
        print(f"❌ Generation failed for {model_type}: {e}")
        return None, None

def compare_models(test_audio_path: str, test_text: str = "Hello, this is a test of the quantized voice model."):
    """Compare standard vs quantized models."""
    device = "cuda" if torch.cuda.is_available() else "cpu"
    print(f"🚀 Starting model comparison on device: {device}")
    
    results = {}
    
    # Test standard model
    generator_std, load_time_std, memory_std = test_model_loading("standard", device)
    if generator_std:
        gen_time_std, output_std = test_generation(generator_std, "standard", test_text, test_audio_path)
        results["standard"] = {
            "load_time": load_time_std,
            "memory": memory_std,
            "generation_time": gen_time_std,
            "output_file": output_std
        }
    
    # Clear memory between tests
    del generator_std
    clear_memory()
    time.sleep(2)  # Give some time for cleanup
    
    # Test quantized model
    generator_4bit, load_time_4bit, memory_4bit = test_model_loading("4bit_quantized", device)
    if generator_4bit:
        gen_time_4bit, output_4bit = test_generation(generator_4bit, "4bit_quantized", test_text, test_audio_path)
        results["4bit_quantized"] = {
            "load_time": load_time_4bit,
            "memory": memory_4bit,
            "generation_time": gen_time_4bit,
            "output_file": output_4bit
        }
    
    # Print comparison summary
    print(f"\n{'='*60}")
    print("COMPARISON SUMMARY")
    print(f"{'='*60}")
    
    if "standard" in results and "4bit_quantized" in results:
        std = results["standard"]
        q4bit = results["4bit_quantized"]
        
        print(f"Loading Time:")
        print(f"  Standard:    {std['load_time']:.2f}s")
        print(f"  4-bit:       {q4bit['load_time']:.2f}s")
        print(f"  Difference:  {q4bit['load_time'] - std['load_time']:+.2f}s")
        
        print(f"\nGPU Memory Usage:")
        print(f"  Standard:    {std['memory']['gpu_mb']:.1f}MB")
        print(f"  4-bit:       {q4bit['memory']['gpu_mb']:.1f}MB")
        print(f"  Reduction:   {std['memory']['gpu_mb'] - q4bit['memory']['gpu_mb']:.1f}MB "
              f"({(1 - q4bit['memory']['gpu_mb'] / std['memory']['gpu_mb']) * 100:.1f}%)")
        
        if std['generation_time'] and q4bit['generation_time']:
            print(f"\nGeneration Time:")
            print(f"  Standard:    {std['generation_time']:.2f}s")
            print(f"  4-bit:       {q4bit['generation_time']:.2f}s")
            print(f"  Difference:  {q4bit['generation_time'] - std['generation_time']:+.2f}s")
            
        print(f"\nOutput Files:")
        print(f"  Standard:    {std['output_file']}")
        print(f"  4-bit:       {q4bit['output_file']}")
        print(f"\n💡 Listen to both files to compare audio quality!")
    
    return results

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) < 2:
        print("Usage: python test_quantization.py <path_to_test_audio.wav> [test_text]")
        print("Example: python test_quantization.py promo_1.wav \"Hello from the quantized model\"")
        sys.exit(1)
    
    test_audio_path = sys.argv[1]
    test_text = sys.argv[2] if len(sys.argv) > 2 else "Hello, this is a test of the quantized voice model."
    
    if not Path(test_audio_path).exists():
        print(f"❌ Test audio file not found: {test_audio_path}")
        sys.exit(1)
    
    print(f"🎤 Using test audio: {test_audio_path}")
    print(f"📝 Test text: {test_text}")
    
    results = compare_models(test_audio_path, test_text)
