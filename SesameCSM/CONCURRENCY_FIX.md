# Concurrency Fix for CSM Voice API

## Problem Description

The original CSM Voice API had a critical concurrency issue where multiple parallel calls to the same model would cause corrupted output. This happened because:

1. **Shared Global State**: The `generator` and `whisper_model` were global variables shared across all requests
2. **Stateful Model Caches**: The CSM model uses KV caches that maintain state between calls
3. **GPU Memory Conflicts**: Multiple concurrent requests tried to use the same GPU memory and model weights simultaneously
4. **No Request Serialization**: FastAPI handles requests concurrently by default, but the models weren't thread-safe

## Solution Implemented

### 1. Async Locks for Model Access
- Added `csm_model_lock` and `whisper_model_lock` using `asyncio.Lock()`
- These locks ensure only one request can use each model at a time
- Maintains FastAPI's async nature while serializing model access

### 2. Thread-Safe Wrapper Functions
- `generate_speech_safely()`: Wraps CSM model generation with proper locking
- `transcribe_and_clean_audio()`: Made async and added Whisper model locking
- Both functions reset model caches to ensure clean state

### 3. Request Tracking and Monitoring
- Added request ID tracking for better debugging
- Active request counter to monitor concurrent load
- Queue status endpoint (`/queue-status`) to monitor lock contention

### 4. Enhanced Logging
- Request-specific logging with unique IDs
- Lock acquisition/release logging
- Performance metrics (generation time, request duration)

## Key Changes Made

### Modified Files:
- `voice_api.py`: Main API file with concurrency fixes

### New Files:
- `test_concurrency.py`: Test script to verify the fix works
- `CONCURRENCY_FIX.md`: This documentation

## How It Works

1. **Request Arrives**: FastAPI receives a request and assigns it a unique ID
2. **Whisper Lock**: Request waits for Whisper model lock to transcribe audio
3. **CSM Lock**: Request waits for CSM model lock to generate speech
4. **Clean State**: Model caches are reset before generation
5. **Generation**: Audio is generated safely without interference
6. **Cleanup**: Locks are released, request metrics updated

## Testing the Fix

Use the provided test script to verify concurrent requests work properly:

```bash
# Install required dependencies
pip install aiohttp

# Run the test (you need a WAV file for testing)
python test_concurrency.py path/to/test_voice.wav 5
```

The test will:
- Send 5 concurrent requests to the API
- Monitor queue status and health
- Report success/failure rates and timing

## Monitoring Endpoints

### Health Check with Concurrency Info
```bash
curl http://localhost:8000/health
```

Returns:
```json
{
  "status": "healthy",
  "device": "cuda",
  "csm_model_loaded": true,
  "whisper_model_loaded": true,
  "concurrency_info": {
    "active_requests": 2,
    "total_requests": 15,
    "csm_model_locked": true,
    "whisper_model_locked": false
  }
}
```

### Queue Status
```bash
curl http://localhost:8000/queue-status
```

Returns:
```json
{
  "active_requests": 2,
  "total_requests": 15,
  "csm_model_locked": true,
  "whisper_model_locked": false,
  "queue_info": {
    "csm_waiters": 3,
    "whisper_waiters": 0
  }
}
```

## Performance Considerations

- **Throughput**: Requests are now serialized, so total throughput is limited by single-request performance
- **Latency**: Individual requests may wait in queue, increasing latency under load
- **Memory**: No memory conflicts or corruption issues
- **Reliability**: Guaranteed correct output for all requests

## Alternative Solutions Considered

1. **Model Replication**: Load multiple model instances (high memory cost)
2. **Process Pools**: Separate processes for each request (high overhead)
3. **Request Queuing**: External queue system (added complexity)

The async lock solution was chosen for its simplicity and effectiveness.

## Future Improvements

1. **Model Pooling**: Load multiple model instances for higher throughput
2. **Request Batching**: Batch compatible requests together
3. **Adaptive Scaling**: Scale model instances based on load
4. **Caching**: Cache results for identical requests
